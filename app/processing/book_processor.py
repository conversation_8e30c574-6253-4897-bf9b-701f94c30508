import hashlib
import logging
from datetime import datetime, timedelta, timezone
from pathlib import Path
from typing import Any

from uuid_extensions import uuid7

from app import settings
from app.database.queries import check_book_duplicates
from app.processing.anthology_detector import AnthologyDetector
from app.processing.archive_processor import ArchiveProcessor
from app.processing.artifact_saver import save_artifact
from app.processing.book_data_builder import BookDataBuilder
from app.processing.canonical_model import CanonicalBook
from app.processing.database_saver import DatabaseSaver
from app.processing.date_extractor import (
    extract_best_date,
)
from app.processing.error_handler import (
    ProcessingError,
    QuarantineError,
    QuarantineType,
)
from app.processing.file_manager import FileManager
from app.processing.fragment_detector import FragmentDetector
from app.processing.hash_computer import HashComputer
from app.processing.parser_dispatcher import ParserDispatcher
from app.processing.pruner import prune_book
from app.processing.queue_manager import TaskQueueManager
from app.processing.small_book_detector import SmallBookDetector


class BookProcessor:
    """Высокоуровневый процессор для обработки книг.

    Инкапсулирует всю бизнес-логику обработки книги из воркера
    """

    def __init__(self, file_manager: FileManager = None):
        self.logger = logging.getLogger(__name__)

        # Используем переданный FileManager или создаём новый, если не передан
        self.file_manager = (
            file_manager
            if file_manager is not None
            else FileManager(enable_quarantine_processing=settings.QUARANTINE_PROCESSING_ENABLED)
        )
        self.archive_processor = ArchiveProcessor()
        self.parser_dispatcher = ParserDispatcher()
        self.hash_computer = HashComputer()
        self.database_saver = DatabaseSaver()
        self.book_data_builder = BookDataBuilder()
        self.queue_manager = TaskQueueManager()

        # Детекторы качества книг
        self.fragment_detector = FragmentDetector()  # Детектор фрагментов
        self.small_book_detector = SmallBookDetector()  # Детектор маленьких книг
        self.anthology_detector = AnthologyDetector()  # Детектор антологий

    def process(self, task_data: dict[str, Any], in_progress_path: Path) -> dict[str, Any]:
        """Выполняет полную обработку книги.

        Args:
            task_data: Данные задачи из очереди
            in_progress_path: Путь к файлу в обработке

        Returns:
            Результат обработки с информацией о выполненных этапах

        Raises:
            QuarantineError: При ошибках бизнес-логики
            Exception: При критических системных ошибках

        """
        temp_dir = None

        try:
            self.logger.debug(f"📖 Начинаем обработку: {in_progress_path.name}")

            # Этап 0: Извлечение source_id и source_type из задачи
            source_id = task_data.get("source_id")
            source_type = task_data.get("source_type")

            # Этап 1: Извлечение содержимого
            temp_dir = self._extract_content(in_progress_path)
            main_book_file = self._find_main_book_file(temp_dir)

            # Этап 2: Парсинг в каноническую модель
            canonical_book = self._parse_to_canonical(main_book_file)

            # Установка source_info в каноническую модель
            canonical_book.source_id = source_id
            canonical_book.source_type = source_type

            # Этап 2.5: Проверка на ознакомительный фрагмент
            self._check_fragment(canonical_book)

            # Этап 3: Постобработка канонической модели
            book_id = self._postprocess_canonical_book(canonical_book, main_book_file)

            # Этап 4: Проверка дубликатов
            duplicate_result = self._check_duplicates(canonical_book)
            if duplicate_result["should_skip"]:
                return {
                    "status": "skipped",
                    "reason": "duplicate",
                    "temp_dir": temp_dir,
                }

            # Этап 5: Сохранение в БД и очередь RAG
            self._save_and_enqueue(canonical_book, task_data, book_id, duplicate_result["hashes"])

            self.logger.info(f"✅ Книга успешно обработана: {canonical_book.title} (ID: {book_id})")

            return {
                "status": "success",
                "book_id": book_id,
                "title": canonical_book.title,
                "temp_dir": temp_dir,
            }

        except Exception:
            # Очистка временных файлов при ошибке
            if temp_dir:
                self.file_manager.cleanup_temp_files(temp_dir)
            raise

    def _extract_content(self, in_progress_path: Path) -> Path:
        """Извлекает содержимое архива."""
        temp_dir = self.archive_processor.extract_archive(in_progress_path)
        self.logger.debug(f"Архив извлечен в: {temp_dir}")
        return temp_dir

    def _find_main_book_file(self, temp_dir: Path) -> Path:
        """Находит основной файл книги в извлеченном архиве."""
        book_files = self.archive_processor.find_book_files(temp_dir)

        if not book_files:
            raise QuarantineError("В архиве не найдено книжных файлов")

        main_book_file = book_files[0]
        self.logger.debug(f"Найден основной файл книги: {main_book_file.name}")
        return main_book_file

    def _parse_to_canonical(self, book_file: Path):
        """Парсит файл книги в каноническую модель."""
        canonical_book = self.parser_dispatcher.parse_to_canonical(book_file)

        # Сохраняем ссылку на FB2 трансформер для проверки broken_footnotes
        # Это нужно для детекции проблем со сносками в _check_broken_footnotes()
        self._last_fb2_transformer = getattr(self.parser_dispatcher, '_last_transformer', None)

        self.logger.debug(f"Книга распарсена: {canonical_book.title}")
        return canonical_book

    def _determine_quarantine_type(self, canonical_book: CanonicalBook) -> tuple[QuarantineType, str] | None:
        """Определяет тип карантина для книги на основе детекторов качества.

        Args:
            canonical_book: Каноническая модель книги

        Returns:
            Кортеж (тип_карантина, причина) или None если книга прошла все проверки
        """
        # Проверка на ознакомительные фрагменты (приоритет 1)
        if self.fragment_detector.is_fragment(canonical_book):
            author_info = self._format_author_info(canonical_book)
            reason = (
                f"Обнаружен ознакомительный фрагмент: '{canonical_book.title}' "
                f"от {author_info}. Фрагменты нарушают дедупликацию и блокируют "
                f"загрузку полных версий книг."
            )
            return QuarantineType.TRIAL, reason

        # Проверка на структурные аномалии (приоритет 2)
        quarantine_type = self.small_book_detector.check_book_structure(canonical_book)
        if quarantine_type:
            reason = self.small_book_detector.get_rejection_reason(canonical_book)
            return quarantine_type, reason

        # Проверка на антологии/сборники (приоритет 3)
        if self.anthology_detector.is_anthology(canonical_book):
            reason = self.anthology_detector.get_anthology_reason(canonical_book)
            return QuarantineType.ANTHOLOGIES, reason

        # Проверка на проблемы со сносками (приоритет 4)
        # ВАЖНО: Отсутствие сносок в файле - это НЕ ошибка
        # Ошибка только когда есть ссылки на сноски, но сами сноски не найдены
        footnotes_result = self._check_broken_footnotes()
        if footnotes_result:
            return footnotes_result

        return None  # Книга прошла все проверки

    def _check_broken_footnotes(self) -> tuple[QuarantineType, str] | None:
        """Проверяет наличие сломанных сносок в последней обработанной книге.

        Логика согласно предложению:
        - Если в файле нет id сносок вообще → НЕ ошибка
        - Если id сносок есть, но мы их не определили → карантин FOOTNOTES

        Returns:
            Кортеж (QuarantineType.FOOTNOTES, причина) или None если проблем нет
        """
        # Получаем трансформер из последнего парсинга
        if not hasattr(self, '_last_fb2_transformer') or not self._last_fb2_transformer:
            return None

        transformer = self._last_fb2_transformer

        # Проверяем есть ли сломанные сноски
        if hasattr(transformer, 'has_broken_footnotes') and transformer.has_broken_footnotes():
            broken_footnotes = transformer.get_broken_footnotes()
            broken_count = len(broken_footnotes)

            # Формируем детальную причину для анализа
            footnote_ids = ', '.join(broken_footnotes[:5])  # Показываем первые 5 ID
            if broken_count > 5:
                footnote_ids += f" и еще {broken_count - 5}"

            reason = (
                f"Обнаружены нераспарсенные сноски: {broken_count} ссылок "
                f"на несуществующие ID ({footnote_ids}). Требуется анализ "
                f"и доработка эвристики определения сносок."
            )

            return QuarantineType.FOOTNOTES, reason

        return None

    def _format_author_info(self, canonical_book: CanonicalBook) -> str:
        """Форматирует информацию об авторах книги."""
        if not canonical_book.authors:
            return "Неизвестный автор"

        authors_list = []
        for author in canonical_book.authors:
            name_parts = [
                author.first_name,
                author.middle_name,
                author.last_name,
            ]
            full_name = " ".join(filter(None, name_parts))
            if author.nickname:
                full_name += f" ({author.nickname})"
            authors_list.append(full_name)
        return ", ".join(authors_list)

    def _check_fragment(self, canonical_book) -> None:
        """Проверяет качество книги и определяет нужен ли карантин.

        Это комплексная проверка для предотвращения попадания некачественных книг в основную коллекцию.
        Включает проверки на фрагменты, маленькие книги и антологии.

        Raises:
            QuarantineError: Если книга должна быть помещена в карантин

        """
        # Определяем тип карантина с помощью детекторов
        quarantine_result = self._determine_quarantine_type(canonical_book)

        if quarantine_result:
            quarantine_type, reason = quarantine_result

            self.logger.warning(f"🚫 {reason}")

            # Генерируем типизированную QuarantineError для правильной обработки в воркере
            raise QuarantineError(
                reason,
                quarantine_type=quarantine_type,
                details={
                    "title": canonical_book.title,
                    "authors": self._format_author_info(canonical_book),
                    "chapters_count": len(canonical_book.chapters),
                    "quarantine_category": quarantine_type.value,
                },
            )

    def _postprocess_canonical_book(self, canonical_book, main_book_file: Path) -> str:
        """Выполняет постобработку канонической модели:
        - Извлечение даты и генерация ID
        - Сохранение информации о дате в модели
        - Очистка данных
        БЕЗ сохранения артефакта (перенесено в двухфазную логику)
        """
        # Генерация ID на основе лучшей даты (теперь всегда в UTC)
        best_date, date_source = extract_best_date(canonical_book.raw_source_model, main_book_file)

        # ==================================================================================
        # ОБРАБОТКА ДАТ ДО 1970 ГОДА ДЛЯ UUID v7
        # ==================================================================================
        #
        # UUID v7 по спецификации RFC 9562 использует 48-битный Unix timestamp в миллисекундах,
        # который по определению не может быть отрицательным (до 1 января 1970 00:00:00 UTC).
        #
        # ПРОБЛЕМА: Многие книги имеют даты публикации до 1970 года (например, классическая
        # литература), что делает невозможным прямое использование их дат для UUID v7.
        #
        # РЕШЕНИЕ: Для книг с датами до 1970 года создается "синтетическая дата" на основе
        # криптографического хеша метаданных книги, размещенная в диапазоне 1970-1980 гг.
        #
        # ПРЕИМУЩЕСТВА ДАННОГО ПОДХОДА:
        # 1. Архитектурная консистентность - все книги используют UUID v7
        # 2. Извлекаемость даты - из UUID всегда можно получить timestamp
        # 3. Уникальность - разные книги получают разные синтетические даты
        # 4. Стабильность - одна книга всегда получает одну и ту же синтетическую дату
        # 5. Сохранение реальной даты - оригинальная дата сохраняется в метаданных
        #
        # АЛЬТЕРНАТИВЫ И ИХ НЕДОСТАТКИ:
        # - UUID v4 (случайный): Теряется возможность извлечения даты из ID
        # - Фиксированная дата 1970-01-01: Нарушается уникальность ID для разных книг
        # - Смешанные типы UUID: Усложняется архитектура и логика обработки
        #
        if best_date.timestamp() < 0:
            # Создаем уникальную подпись книги из title + authors + source_id
            book_signature = f"{canonical_book.title}_{len(canonical_book.authors)}_{canonical_book.source_id}"
            hash_value = int(
                hashlib.md5(book_signature.encode("utf-8"), usedforsecurity=False).hexdigest()[:8],
                16,
            )

            # Создаем дату в диапазоне 1970-1980 на основе хеша (10 лет после эпохи)
            epoch_start = datetime(1970, 1, 1, tzinfo=timezone.utc)
            offset_seconds = hash_value % (365 * 24 * 3600 * 10)  # 10 лет
            uuid_date = epoch_start + timedelta(seconds=offset_seconds)

            self.logger.debug(f"Дата книги {best_date} до эпохи Unix. Создана уникальная дата для UUID7: {uuid_date}")
        else:
            uuid_date = best_date

        # Преобразуем timestamp в наносекунды для UUIDv7
        timestamp_ns = int(uuid_date.timestamp() * 1e9)
        book_id = str(uuid7(ns=timestamp_ns))
        self.logger.debug(f"Сгенерирован ID книги (UUIDv7) на основе даты {uuid_date}: {book_id}")

        # Сохраняем информацию о дате в модели - теперь в формате ISO с UTC
        canonical_book.book_id_generation_date = best_date.isoformat()

        # Используем источник даты полученный из extract_best_date
        canonical_book.book_id_date_source = date_source
        self.logger.debug(f"Источник даты для book_id: {date_source}")

        # Очистка канонической модели
        prune_book(canonical_book)
        self.logger.debug("Каноническая модель очищена")

        return book_id

    def _check_duplicates(self, canonical_book) -> dict[str, Any]:
        """Проверяет книгу на дубликаты и принимает бизнес-решения.

        Returns:
            Dict с результатом проверки и хэшами:
            {
                "should_skip": bool,
                "hashes": dict
            }
        """
        try:
            hashes = self.hash_computer.compute_hashes(canonical_book)
            duplicate_db_record = check_book_duplicates(hashes["metadata_hash"])

            should_skip = duplicate_db_record is not None
            if should_skip:
                self.logger.info(
                    f"Дубликат книги '{canonical_book.title}' пропущен. ID существующей записи: {duplicate_db_record.get('existing_book_id') if duplicate_db_record else 'unknown'}"
                )

            return {"hashes": hashes, "should_skip": should_skip}

        except Exception as e:
            # NB: Используем именно ProcessingError (RETRY).
            # Несмотря на то, что ErrorHandler помечает такую ошибку как «retry»,
            # текущая реализация BookWorker **не** возвращает задачу в очередь,
            # а сразу отправляет файл в карантин и финализирует задачу.
            # Поэтому бесконечных повторных попыток не возникает.
            # Если в будущем появится логика `return_task_to_queue`, этот участок
            # потребуется пересмотреть (возможно, заменить на FatalError либо
            # явный возврат задачи).
            self.logger.error(f"Ошибка проверки дубликатов: {e}")
            # В случае ошибки БД лучше не сохранять книгу, чтобы избежать дублей
            raise ProcessingError(f"Ошибка доступа к БД при проверке дубликатов: {e}") from e

    def _save_and_enqueue(
        self,
        canonical_book,
        task_data: dict[str, Any],
        book_id: str,
        hashes: dict[str, str],
    ):
        """Двухфазное сохранение книги для обеспечения атомарности данных.

        Фаза 1: Сохранение метаданных в БД (process_status=10)
        Фаза 2: Создание артефакта + обновление статуса (process_status=20)

        Это гарантирует целостность между БД и файловой системой.
        """
        # Подготавливаем DTO для сохранения
        book_dto = self.book_data_builder.build_book_dto_from_canonical(
            canonical_book,
            task_data,
            hashes["metadata_hash"],
        )

        # ФАЗА 1: Сохраняем метаданные в БД со статусом 10 (метаданные сохранены)
        self.database_saver.save_book_metadata_only(book_dto, book_id)
        self.logger.debug(f"Фаза 1: Метаданные книги сохранены в БД (статус 10): {book_id}")

        # ФАЗА 2: Создаем артефакт на диске
        artifact_path = save_artifact(canonical_book, book_id)
        self.logger.debug(f"Фаза 2: Каноническая модель сохранена: {artifact_path}")

        # ФАЗА 2: Обновляем статус на полностью обработано (20)
        self.database_saver.update_book_status(book_id, 20)
        self.logger.debug(f"Фаза 2: Статус книги обновлен на 20 (полностью обработано): {book_id}")

        # Ставим задачу в очередь для RAG-пайплайна
        self.queue_manager.enqueue_rag_task(book_id)
        self.logger.debug(f"Книга поставлена в очередь RAG: {book_id}")
