# Система карантина - Операционное руководство

> **Документация:** Полное руководство по работе системы карантина, типам обработки файлов и операционным процедурам.

## 📋 Обзор системы

Система карантина обеспечивает изоляцию и дифференцированную обработку проблемных файлов книг. Использует паттерн **Strategy** для применения различных стратегий обработки в зависимости от типа проблемы.

### Принципы работы:
- **Автоматическая классификация** - файлы помещаются в соответствующие папки по типам проблем
- **Дифференцированная обработка** - каждый тип использует оптимальную стратегию постобработки
- **Сохранение ценных данных** - потенциально полезные файлы сохраняются через перепаковку
- **Экономия места** - мусорные файлы заменяются пустыми маркерами

## 🏗️ Структура карантина и операции

```
quarantine/
├── trial/           → 🗑️ EMPTY_MARKER (ZIP → пустой маркер, экономия ~99%)
├── small_content/   → ❄️ COLD_STORAGE (ZIP → перепаковка без картинок)
├── few_chapters/    → ❄️ COLD_STORAGE (ZIP → перепаковка без картинок)  
├── anthologies/     → ❄️ COLD_STORAGE (ZIP → перепаковка без картинок)
├── error/           → ❄️ COLD_STORAGE (ZIP → перепаковка без картинок)
├── footnotes/       → ❄️ COLD_STORAGE (ZIP → перепаковка без картинок)
└── invalid/         → 🗑️ EMPTY_MARKER (ZIP → пустой маркер, экономия ~99%)
```

## 📊 Типы карантина и стратегии

### 🗑️ EMPTY_MARKER - Полная замена на маркер

**Папки:** `trial/`, `invalid/`

**Логика обработки:**
1. Оригинальный ZIP **полностью заменяется** на минимальный пустой маркер
2. Маркер содержит только:
   - `_marker_info.txt` - метаинформация о причине карантина
   - `_original_{filename}.zip` - пустой файл с именем оригинала
3. **Экономия места:** до 99% от исходного размера

**Применение:**
- **`trial/`** - ознакомительные фрагменты (не представляют ценности)
- **`invalid/`** - поврежденные архивы (восстановлению не подлежат)

### ❄️ COLD_STORAGE - Оптимизированная перепаковка

**Папки:** `small_content/`, `few_chapters/`, `anthologies/`, `error/`, `footnotes/`

**Логика обработки:**
1. **Извлечение** оригинального ZIP архива
2. **Очистка FB2 файлов:**
   - Удаление всех тегов `<binary>` (встроенные изображения)
   - Удаление всех тегов `<image>` (ссылки на изображения)
3. **Перепаковка** с максимальным сжатием (level=9)
4. **Замена** оригинального файла на оптимизированный
5. **Добавление** .error файла с описанием причины

**Применение:**
- **`small_content/`** - книги с недостаточным объемом текста (могут быть ценными рассказами)
- **`few_chapters/`** - книги с малым числом глав (могут быть повестями без разбивки)
- **`anthologies/`** - сборники разных авторов (потенциально ценные)
- **`error/`** - системные ошибки обработки (возможна повторная обработка)
- **`footnotes/`** - книги с нераспарсенными сносками (возможно исправление)

## 🔧 Техническая реализация

### Классы и компоненты

**QuarantineProcessor** - главный координатор постобработки
```python
# Определение стратегии по типу карантина
strategy = self._get_processing_strategy(quarantine_type)

if strategy == "cold_storage":
    return self._process_for_cold_storage(quarantine_file, error_reason)
elif strategy == "empty_marker":
    return self._process_as_empty_marker(quarantine_file, error_reason)
```

**ColdStorageRepacker** - перепаковка с удалением изображений
```python
# Паттерны для удаления изображений из FB2
self.binary_pattern = re.compile(r"<binary[^>]*>.*?</binary>", re.DOTALL)
self.image_pattern = re.compile(r"<image[^>]*(?:\s*/>|>.*?</image>)", re.DOTALL)
```

**EmptyFileCreator** - создание пустых маркеров
```python
# Создание минимального ZIP с метаинформацией
with zipfile.ZipFile(target_file, "w", compression=zipfile.ZIP_DEFLATED) as marker_zip:
    marker_zip.writestr("_marker_info.txt", marker_info)
    marker_zip.writestr(f"_original_{original_name}", "")
```

### Реестр стратегий

**QuarantineStrategyRegistry** использует паттерн Strategy:

```python
class ColdStorageStrategy(QuarantineStrategy):
    def can_handle(self, quarantine_type: QuarantineType) -> bool:
        return quarantine_type in (
            QuarantineType.ANTHOLOGIES,
            QuarantineType.FOOTNOTES, 
            QuarantineType.ERROR,
            QuarantineType.SMALL_CONTENT,
            QuarantineType.FEW_CHAPTERS,
        )
```

## 📈 Детекция и критерии

### SmallBookDetector - обновленная логика

**Два независимых критерия:**

1. **`_is_small_content()`** - недостаточный объем текста
   ```python
   total_content = sum(len(ch.content_md) for ch in canonical_book.chapters)
   return total_content < self.min_content_chars  # по умолчанию 10000
   ```

2. **`_has_few_chapters()`** - аномально мало глав
   ```python
   chapters_count = len(canonical_book.chapters)
   return chapters_count < self.min_chapters  # по умолчанию 4
   ```

**Приоритетность проверки:**
1. Сначала проверяется объем контента → `QuarantineType.SMALL_CONTENT`
2. Затем количество глав → `QuarantineType.FEW_CHAPTERS`
3. Книга проходит проверки → обработка продолжается

### Детектор сносок - новая логика

**Принцип работы:**

```python
def _check_broken_footnotes(self) -> tuple[QuarantineType, str] | None:
    # Отсутствие сносок в файле → НЕ ошибка
    # Наличие ссылок на несуществующие сноски → карантин FOOTNOTES
    if transformer.has_broken_footnotes():
        return QuarantineType.FOOTNOTES, reason
    return None
```

**Логика детекции:**
- **Файл без сносок** - нормальная ситуация, не является ошибкой
- **Файл с корректными сносками** - все ссылки `<a href="#note_1">` имеют соответствующие определения
- **Файл с broken_footnotes** - есть ссылки на сноски, но сами сноски не найдены парсером

**Цель карантина FOOTNOTES:**
- Сбор файлов с проблемными сносками для анализа
- Доработка эвристики определения сносок в FB2Parser
- Исключение ложных срабатываний на файлы без сносок

**Приоритетность проверки (обновлено):**
1. Ознакомительные фрагменты → `QuarantineType.TRIAL`
2. Структурные аномалии → `QuarantineType.SMALL_CONTENT` / `FEW_CHAPTERS`
3. Антологии/сборники → `QuarantineType.ANTHOLOGIES`
4. **Проблемы со сносками → `QuarantineType.FOOTNOTES`**
5. Книга проходит все проверки → обработка продолжается

## 🎯 Результаты обработки

### Экономия места

**EMPTY_MARKER:**
- Исходный файл: 1-50 МБ
- Маркер: 1-5 КБ
- **Экономия: 95-99%**

**COLD_STORAGE:**
- Удаление изображений: 30-70% экономии
- Максимальное сжатие: +10-20% экономии
- **Общая экономия: 40-80%**

### Статистика обработки

```python
{
    "status": "success",
    "original_size": 15728640,        # 15 МБ
    "final_size": 4718592,           # 4.5 МБ 
    "compression_ratio": 0.7,        # 70% экономии
    "fb2_files_processed": 1,
    "strategy": "cold_storage"
}
```

## 🔍 Мониторинг и аналитика

### Команды проверки

```bash
# Общее количество файлов в карантине
find /sources/*/quarantine/ -name "*.zip" | wc -l

# Статистика по типам
for type in trial small_content few_chapters anthologies error footnotes invalid; do
    count=$(find /sources/*/quarantine/$type -name "*.zip" 2>/dev/null | wc -l)
    echo "$type: $count файлов"
done

# Размер карантина по папкам  
du -sh /sources/*/quarantine/*/ | sort -h
```

### Метрики для мониторинга

- **Коэффициент карантина** - процент файлов в карантине от общего числа
- **Распределение по типам** - баланс между стратегиями обработки
- **Эффективность сжатия** - экономия места в cold storage
- **Время обработки** - производительность постобработки

## 🚨 Операционные процедуры

### Восстановление из карантина

**COLD_STORAGE файлы:**
```bash
# Файлы можно попробовать переобработать после исправления проблем
mv /sources/X/quarantine/error/book.zip /sources/X/new/
```

**EMPTY_MARKER файлы:**
```bash
# Маркеры содержат информацию о причине, но восстановлению не подлежат
cat /sources/X/quarantine/trial/book.zip  # просмотр _marker_info.txt
```

### Очистка старого карантина

```bash
# Удаление маркеров старше 30 дней
find /sources/*/quarantine/trial/ -name "*.zip" -mtime +30 -delete
find /sources/*/quarantine/invalid/ -name "*.zip" -mtime +30 -delete

# Архивирование cold storage старше 90 дней
find /sources/*/quarantine/error/ -name "*.zip" -mtime +90 -exec tar -czf archive.tar.gz {} \;
```

### Анализ проблемных файлов

```python
# Получение статистики через QuarantineProcessor
processor = QuarantineProcessor()
stats = processor.get_processing_statistics(Path("/sources/X/quarantine"))

print(f"Всего файлов: {stats['total_files']}")
print(f"По стратегиям: {stats['by_strategy']}")
print(f"По типам: {stats['by_type']}")
```

## 🎮 Миграция при изменении типов

При изменении логики карантина (например, переименование `SMALL` → `SMALL_CONTENT`, `FEW_CHAPTERS`):

```python
# Скрипт миграции для перераспределения файлов
# tools/migrate_quarantine_old_to_new.py

# 1. Перепарсивание файлов из старых папок
# 2. Применение новой логики детекции  
# 3. Перемещение в соответствующие новые папки
# 4. Создание отчета о миграции
```

---

**📊 Связанная документация:**
- [Архитектура парсинга](../architecture/parsing_architecture.md) - общие принципы обработки ошибок
- [Архитектурные паттерны](../../ai-docs/architectural-patterns.md) - паттерны стратегий 
- [Redis очереди](../redis_queues.md) - интеграция с системой задач

**🔧 Инструменты:**
- `tools/run_quarantine_demo.py` - демонстрация работы карантина
- `app/processing/quarantine_processor.py` - основная логика
- `app/processing/quarantine_strategies.py` - реестр стратегий 